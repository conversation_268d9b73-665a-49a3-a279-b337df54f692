import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'Pharmalien WebView',
      home: WebViewApp(),
    );
  }
}

class WebViewApp extends StatefulWidget {
  const WebViewApp({super.key});

  @override
  _WebViewAppState createState() => _WebViewAppState();
}

class _WebViewAppState extends State<WebViewApp> with TickerProviderStateMixin {
  late WebViewController _controller;
  String _currentUrl = 'http://vps5.sophatel.com:4201';
  bool _isExternalSite = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  // Define your main domain
  static const String mainDomain = 'http://vps5.sophatel.com:4201';

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onUrlChange: (UrlChange urlChange) {
            _handleUrlChange(urlChange.url ?? '');
          },
          onPageFinished: (String url) {
            // Add any actions to perform when the page finishes loading
            // Inject JavaScript to set localStorage variable
            _controller.runJavaScript('localStorage.setItem("fromFlutterWebView", "true");');
            _handleUrlChange(url);
          },
          onWebResourceError: (WebResourceError error) {
            print('WebView Error: ${error.description}');
            // You can show a custom error page or retry logic here
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow all navigation requests
            return NavigationDecision.navigate;
          },
        ),
      )
      ..setUserAgent("fromFlutterWebView")
      ..loadRequest(Uri.parse(mainDomain));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleUrlChange(String url) {
    setState(() {
      _currentUrl = url;
      _isExternalSite = !url.startsWith(mainDomain);
    });

    if (_isExternalSite) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    // Debug print to see current URL (optional)
    print('Current URL: $_currentUrl, Is External: $_isExternalSite');
  }

  void _goBackToMainSite() {
    _controller.loadRequest(Uri.parse(mainDomain));
  }

  void _goBack() async {
    if (await _controller.canGoBack()) {
      await _controller.goBack();
    } else {
      _goBackToMainSite();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // WebView
            WebViewWidget(controller: _controller),

            // Floating Back Button - only show when on external site
            if (_isExternalSite)
              Positioned(
                top: 16,
                left: 16,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(25),
                          onTap: _goBack,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                const SizedBox(width: 4),
                                const Text(
                                  'Retour',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Container(
                                  width: 1,
                                  height: 20,
                                  color: Colors.white.withOpacity(0.5),
                                ),
                                const SizedBox(width: 8),
                                GestureDetector(
                                  onTap: _goBackToMainSite,
                                  child: const Row(
                                    children: [
                                      Icon(
                                        Icons.home,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        'Accueil',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),


          ],
        ),
      ),
    );
  }
}
